# -*- coding: utf-8 -*-
"""
Cross Validation dengan Pendekatan Procedural Programming
Klasifikasi Burung menggunakan MobileNetV2
Tanpa menggunakan function - step by step approach
"""

# Import semua library yang diperlukan
import tensorflow as tf
import os
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from tensorflow.keras.layers import GlobalAveragePooling2D, Dropout, Dense
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.applications import MobileNetV2
from tensorflow.keras.models import Model
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from PIL import Image

print("=== CROSS VALIDATION DENGAN PENDEKATAN PROCEDURAL ===")
print("Memulai proses klasifikasi burung dengan MobileNetV2")

# ===== LANGKAH 1: KONFIGURASI AWAL =====
print("\n=== LANGKAH 1: Kon<PERSON>gurasi <PERSON> ===")

# Definisi parameter model
input_shape = (224, 224, 3)
num_classes = 5
batch_size = 128
n_splits = 5  # Jumlah fold untuk cross validation

print(f"Input shape: {input_shape}")
print(f"Jumlah kelas: {num_classes}")
print(f"Batch size: {batch_size}")
print(f"Jumlah fold CV: {n_splits}")

# ===== LANGKAH 2: PERSIAPAN DATA LOKAL =====
print("\n=== LANGKAH 2: Persiapan Data Lokal ===")

# Definisi direktori dataset lokal
# Sesuaikan path ini dengan lokasi dataset Anda
dataset_base_dir = "Dataset"  # Folder dataset di direktori yang sama dengan script
train_data_dir = os.path.join(dataset_base_dir, "train")
valid_data_dir = os.path.join(dataset_base_dir, "validation")

print(f"Direktori dataset: {dataset_base_dir}")
print(f"Direktori training: {train_data_dir}")
print(f"Direktori validasi: {valid_data_dir}")

# Cek apakah direktori dataset ada
if not os.path.exists(dataset_base_dir):
    print(f"ERROR: Direktori dataset '{dataset_base_dir}' tidak ditemukan!")
    print("Silakan buat struktur folder sebagai berikut:")
    print("dataset/")
    print("├── train/")
    print("│   ├── kelas1/")
    print("│   ├── kelas2/")
    print("│   ├── kelas3/")
    print("│   ├── kelas4/")
    print("│   └── kelas5/")
    print("└── validation/")
    print("    ├── kelas1/")
    print("    ├── kelas2/")
    print("    ├── kelas3/")
    print("    ├── kelas4/")
    print("    └── kelas5/")
    exit(1)

# Cek struktur direktori
print("\nMemeriksa struktur dataset...")
if os.path.exists(train_data_dir):
    train_classes = [d for d in os.listdir(train_data_dir) if os.path.isdir(os.path.join(train_data_dir, d))]
    print(f"Kelas di direktori training: {train_classes}")
    print(f"Jumlah kelas training: {len(train_classes)}")
else:
    print(f"ERROR: Direktori training '{train_data_dir}' tidak ditemukan!")
    exit(1)

if os.path.exists(valid_data_dir):
    valid_classes = [d for d in os.listdir(valid_data_dir) if os.path.isdir(os.path.join(valid_data_dir, d))]
    print(f"Kelas di direktori validasi: {valid_classes}")
    print(f"Jumlah kelas validasi: {len(valid_classes)}")
else:
    print(f"ERROR: Direktori validasi '{valid_data_dir}' tidak ditemukan!")
    exit(1)

# Verifikasi jumlah kelas
if len(train_classes) != num_classes or len(valid_classes) != num_classes:
    print(f"ERROR: Jumlah kelas tidak sesuai!")
    print(f"Expected: {num_classes} kelas")
    print(f"Found - Training: {len(train_classes)}, Validation: {len(valid_classes)}")
    exit(1)

print("✅ Struktur dataset valid!")

# ===== LANGKAH 3: PENGUMPULAN PATH GAMBAR DAN LABEL =====
print("\n=== LANGKAH 3: Mengumpulkan Path Gambar dan Label ===")

# Inisialisasi list untuk menyimpan path dan label
image_paths = []
labels = []

# Kumpulkan data dari direktori training
print("Mengumpulkan data dari direktori training...")
for class_name in os.listdir(train_data_dir):
    class_path = os.path.join(train_data_dir, class_name)
    if os.path.isdir(class_path):
        print(f"  Memproses kelas: {class_name}")
        for img_file in os.listdir(class_path):
            if img_file.lower().endswith(('.png', '.jpg', '.jpeg')):
                image_paths.append(os.path.join(class_path, img_file))
                labels.append(class_name)

# Kumpulkan data dari direktori validasi
print("Mengumpulkan data dari direktori validasi...")
for class_name in os.listdir(valid_data_dir):
    class_path = os.path.join(valid_data_dir, class_name)
    if os.path.isdir(class_path):
        print(f"  Memproses kelas: {class_name}")
        for img_file in os.listdir(class_path):
            if img_file.lower().endswith(('.png', '.jpg', '.jpeg')):
                image_paths.append(os.path.join(class_path, img_file))
                labels.append(class_name)

print(f"Total gambar yang dikumpulkan: {len(image_paths)}")

# ===== LANGKAH 4: PEMBUATAN MAPPING LABEL =====
print("\n=== LANGKAH 4: Membuat Mapping Label ===")

# Buat mapping label ke indeks
unique_labels = sorted(list(set(labels)))
label_to_idx = {}
idx_to_label = {}

for idx, label in enumerate(unique_labels):
    label_to_idx[label] = idx
    idx_to_label[idx] = label

print("Mapping label ke indeks:")
for label, idx in label_to_idx.items():
    print(f"  {label}: {idx}")

# Konversi label ke indeks
label_indices = []
for label in labels:
    label_indices.append(label_to_idx[label])

# Konversi ke numpy array
image_paths = np.array(image_paths)
label_indices = np.array(label_indices)

print(f"Jumlah kelas unik: {len(unique_labels)}")
print(f"Kelas-kelas: {unique_labels}")

# ===== LANGKAH 5: INISIALISASI CROSS VALIDATION =====
print("\n=== LANGKAH 5: Inisialisasi Cross Validation ===")

# Inisialisasi StratifiedKFold
skf = StratifiedKFold(n_splits=n_splits, shuffle=True, random_state=42)

# Inisialisasi variabel untuk menyimpan hasil
cv_scores = []
fold_histories = []
all_predictions = []
all_true_labels = []

print(f"StratifiedKFold dengan {n_splits} fold telah diinisialisasi")
print("Memulai proses cross validation...")

# ===== LANGKAH 6: LOOP CROSS VALIDATION =====
print("\n=== LANGKAH 6: Proses Cross Validation ===")

fold_counter = 0

# Iterasi melalui setiap fold
for train_idx, val_idx in skf.split(image_paths, label_indices):
    fold_counter += 1
    print(f"\n--- FOLD {fold_counter}/{n_splits} ---")
    
    # Bagi data untuk fold saat ini
    train_paths = image_paths[train_idx]
    val_paths = image_paths[val_idx]
    train_labels = label_indices[train_idx]
    val_labels = label_indices[val_idx]
    
    print(f"Jumlah data training: {len(train_paths)}")
    print(f"Jumlah data validasi: {len(val_paths)}")
    
    # LANGKAH 6.1: Load dan preprocess gambar training
    print(f"Memuat dan memproses gambar training untuk fold {fold_counter}...")
    X_train = []
    y_train = []
    
    for i, path in enumerate(train_paths):
        try:
            # Buka dan proses gambar
            image = Image.open(path)
            image = image.convert('RGB')
            image = image.resize((224, 224))
            image_array = np.array(image) / 255.0
            
            X_train.append(image_array)
            y_train.append(train_labels[i])
            
            # Progress indicator
            if (i + 1) % 100 == 0:
                print(f"  Diproses: {i + 1}/{len(train_paths)} gambar training")
                
        except Exception as e:
            print(f"Error memuat gambar training {path}: {e}")
            continue
    
    # LANGKAH 6.2: Load dan preprocess gambar validasi
    print(f"Memuat dan memproses gambar validasi untuk fold {fold_counter}...")
    X_val = []
    y_val = []
    
    for i, path in enumerate(val_paths):
        try:
            # Buka dan proses gambar
            image = Image.open(path)
            image = image.convert('RGB')
            image = image.resize((224, 224))
            image_array = np.array(image) / 255.0
            
            X_val.append(image_array)
            y_val.append(val_labels[i])
            
            # Progress indicator
            if (i + 1) % 50 == 0:
                print(f"  Diproses: {i + 1}/{len(val_paths)} gambar validasi")
                
        except Exception as e:
            print(f"Error memuat gambar validasi {path}: {e}")
            continue
    
    # Konversi ke numpy array dan categorical
    X_train = np.array(X_train)
    X_val = np.array(X_val)
    y_train = tf.keras.utils.to_categorical(y_train, num_classes)
    y_val = tf.keras.utils.to_categorical(y_val, num_classes)
    
    print(f"Shape data training: {X_train.shape}")
    print(f"Shape data validasi: {X_val.shape}")
    
    # LANGKAH 6.3: Buat model MobileNetV2
    print(f"Membuat model MobileNetV2 untuk fold {fold_counter}...")
    
    # Load MobileNetV2 sebagai base model
    base_model = MobileNetV2(weights='imagenet', include_top=False, input_shape=input_shape)
    
    # Freeze semua layer di base model
    for layer in base_model.layers:
        layer.trainable = False
    
    # Tambahkan custom head
    x = base_model.output
    x = GlobalAveragePooling2D()(x)
    x = Dropout(0.5)(x)
    x = Dense(1024, activation='relu')(x)
    x = Dropout(0.5)(x)
    predictions = Dense(num_classes, activation='softmax')(x)
    
    # Gabungkan base model dan custom head
    model = Model(inputs=base_model.input, outputs=predictions)
    
    # Fine-tune dengan unfreeze beberapa layer terakhir
    for layer in base_model.layers[:-10]:
        layer.trainable = False
    
    print(f"Model untuk fold {fold_counter} telah dibuat")
    print(f"Total parameter: {model.count_params()}")

    # LANGKAH 6.4: Konfigurasi training
    print(f"Mengkonfigurasi training untuk fold {fold_counter}...")

    # Setup learning rate scheduling
    lr_schedule = tf.keras.optimizers.schedules.ExponentialDecay(
        initial_learning_rate=1e-4,
        decay_steps=1000,
        decay_rate=0.9
    )
    optimizer = Adam(learning_rate=lr_schedule)

    # Compile model
    model.compile(optimizer=optimizer, loss='categorical_crossentropy', metrics=['accuracy'])

    # Setup early stopping
    early_stopping = tf.keras.callbacks.EarlyStopping(
        monitor='val_loss',
        patience=3,
        restore_best_weights=True
    )

    # Setup data augmentation untuk training
    train_datagen_cv = ImageDataGenerator(
        rotation_range=20,
        width_shift_range=0.2,
        height_shift_range=0.2,
        shear_range=0.2,
        zoom_range=0.2,
        horizontal_flip=True,
        vertical_flip=True,
        fill_mode='nearest'
    )

    print("Konfigurasi training selesai")

    # LANGKAH 6.5: Training model
    print(f"Memulai training model untuk fold {fold_counter}...")

    history = model.fit(
        train_datagen_cv.flow(X_train, y_train, batch_size=batch_size),
        epochs=30,
        validation_data=(X_val, y_val),
        callbacks=[early_stopping],
        verbose=1,
        steps_per_epoch=len(X_train) // batch_size
    )

    print(f"Training fold {fold_counter} selesai")

    # LANGKAH 6.6: Evaluasi model
    print(f"Mengevaluasi model untuk fold {fold_counter}...")

    val_loss, val_accuracy = model.evaluate(X_val, y_val, verbose=0)
    cv_scores.append(val_accuracy)
    fold_histories.append(history)

    print(f"Fold {fold_counter} - Loss: {val_loss:.4f}, Accuracy: {val_accuracy:.4f}")

    # LANGKAH 6.7: Prediksi untuk fold ini
    print(f"Membuat prediksi untuk fold {fold_counter}...")

    val_predictions = model.predict(X_val, verbose=0)
    val_pred_labels = np.argmax(val_predictions, axis=1)
    val_true_labels = np.argmax(y_val, axis=1)

    # Simpan prediksi untuk analisis keseluruhan
    all_predictions.extend(val_pred_labels)
    all_true_labels.extend(val_true_labels)

    print(f"Prediksi fold {fold_counter} selesai")

    # LANGKAH 6.8: Bersihkan memory
    print(f"Membersihkan memory setelah fold {fold_counter}...")

    del model, X_train, X_val, y_train, y_val, base_model
    tf.keras.backend.clear_session()

    print(f"Fold {fold_counter} selesai!\n")

print("=== SEMUA FOLD CROSS VALIDATION SELESAI ===")

# ===== LANGKAH 7: ANALISIS HASIL CROSS VALIDATION =====
print("\n=== LANGKAH 7: Analisis Hasil Cross Validation ===")

print("\n--- Hasil Cross Validation ---")
print(f"Akurasi setiap fold:")
for i, score in enumerate(cv_scores):
    print(f"  Fold {i+1}: {score:.4f}")

mean_cv_accuracy = np.mean(cv_scores)
std_cv_accuracy = np.std(cv_scores)

print(f"\nRingkasan Statistik:")
print(f"  Mean CV Accuracy: {mean_cv_accuracy:.4f}")
print(f"  Standard Deviation: {std_cv_accuracy:.4f}")
print(f"  95% Confidence Interval: {mean_cv_accuracy:.4f} (+/- {std_cv_accuracy * 2:.4f})")

# ===== LANGKAH 8: VISUALISASI HASIL =====
print("\n=== LANGKAH 8: Membuat Visualisasi ===")

# Buat confusion matrix
class_labels = [idx_to_label[i] for i in range(len(idx_to_label))]
conf_matrix = confusion_matrix(all_true_labels, all_predictions)

print("Membuat confusion matrix...")
plt.figure(figsize=(12, 8))
sns.heatmap(conf_matrix, annot=True, fmt='d',
            xticklabels=class_labels, yticklabels=class_labels,
            cmap='Blues')
plt.xlabel('Predicted Label')
plt.ylabel('True Label')
plt.title('Confusion Matrix - Cross Validation Results')
plt.xticks(rotation=45)
plt.yticks(rotation=0)
plt.tight_layout()
plt.show()

# Classification report
print("\n--- Classification Report ---")
print(classification_report(all_true_labels, all_predictions,
                          target_names=class_labels))

# Plot training history
print("Membuat plot training history...")
plt.figure(figsize=(15, 10))

# Plot accuracy untuk semua fold
plt.subplot(2, 2, 1)
for i, history in enumerate(fold_histories):
    plt.plot(history.history['accuracy'], label=f'Fold {i+1} Train')
    plt.plot(history.history['val_accuracy'], label=f'Fold {i+1} Val', linestyle='--')
plt.title('Model Accuracy Across Folds')
plt.xlabel('Epoch')
plt.ylabel('Accuracy')
plt.legend()
plt.grid(True)

# Plot loss untuk semua fold
plt.subplot(2, 2, 2)
for i, history in enumerate(fold_histories):
    plt.plot(history.history['loss'], label=f'Fold {i+1} Train')
    plt.plot(history.history['val_loss'], label=f'Fold {i+1} Val', linestyle='--')
plt.title('Model Loss Across Folds')
plt.xlabel('Epoch')
plt.ylabel('Loss')
plt.legend()
plt.grid(True)

# Plot CV scores
plt.subplot(2, 2, 3)
plt.bar(range(1, len(cv_scores) + 1), cv_scores)
plt.axhline(y=mean_cv_accuracy, color='r', linestyle='--',
            label=f'Mean: {mean_cv_accuracy:.4f}')
plt.title('Cross Validation Scores')
plt.xlabel('Fold')
plt.ylabel('Accuracy')
plt.legend()
plt.grid(True)

plt.tight_layout()
plt.show()

print("\n=== CROSS VALIDATION PROCEDURAL SELESAI ===")
print("Semua langkah telah berhasil diselesaikan!")
print(f"Hasil akhir - Mean Accuracy: {mean_cv_accuracy:.4f} ± {std_cv_accuracy:.4f}")
